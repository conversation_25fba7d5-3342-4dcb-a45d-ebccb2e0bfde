{"name": "ox_inventory", "version": "1.0.0", "homepage": "web/build", "private": true, "dependencies": {"@floating-ui/react": "^0.26.22", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@types/lodash": "^4.17.7", "@types/node": "^20.14.14", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-transition-group": "^4.4.10", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "lodash": "^4.17.21", "react": "^18.3.1", "react-dnd": "^16.0.1", "react-dnd-touch-backend": "^16.0.1", "react-dom": "^18.3.1", "react-markdown": "^9.0.1", "react-redux": "^9.1.2", "react-transition-group": "^4.4.5", "redux": "^5.0.1", "sass": "^1.77.8", "tailwindcss": "^3.4.9"}, "scripts": {"start": "vite", "watch": "vite build --watch", "build": "tsc && vite build", "preview": "vite preview", "format": "prettier --write \"./src/**/*.{ts,tsx,css}\""}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.25.2", "@redux-devtools/core": "^4.0.0", "@redux-devtools/instrument": "^2.2.0", "@reduxjs/toolkit": "^2.2.7", "@types/react-redux": "^7.1.33", "cross-env": "^7.0.3", "csstype": "^3.1.3", "prettier": "^3.3.3", "typescript": "^5.5.4", "vite": "^5.4.9"}}