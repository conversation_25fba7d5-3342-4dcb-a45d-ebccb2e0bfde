# Clothing Boxes Implementation for ox_inventory

## Overview
Added 3 vertical clothing boxes to the right side of the ox_inventory interface for clothing items:
1. **Mask** - Slot 41
2. **Armor** - Slot 42  
3. **Bag** - Slot 43

## Files Modified

### 1. `web/src/components/inventory/ClothingBoxes.tsx` (NEW)
- New React component that renders 3 vertical clothing boxes
- Each box contains a labeled header and an inventory slot
- Uses slots 41-43 to avoid conflicts with hotbar slots (1-5)
- Integrates with existing inventory system using InventorySlot component

### 2. `web/src/components/inventory/index.tsx`
- Added import for ClothingBoxes component
- Added `<ClothingBoxes />` to the inventory layout after RightInventory

### 3. `web/src/index.scss`
- Added new CSS classes for clothing boxes styling:
  - `.clothing-boxes-wrapper` - Container for all clothing boxes
  - `.clothing-box` - Individual clothing box styling with hover effects
  - `.clothing-box-header` - Header text styling for each box
  - `.clothing-box-slot` - Slot styling that matches inventory slots

## Features

### Visual Design
- **Vertical Layout**: 3 boxes stacked vertically on the right side
- **Hover Effects**: Boxes glow with teal accent color on hover
- **Consistent Styling**: Matches the existing ox_inventory dark theme
- **Responsive**: Boxes maintain proper sizing with the grid system

### Functionality
- **Drag & Drop**: Full integration with existing drag-and-drop system
- **Item Display**: Shows item icons, counts, and weights
- **Context Menu**: Right-click functionality for item actions
- **Tooltips**: Item information tooltips on hover

### Slot Configuration
- **Mask**: Slot 41 - For face masks and head accessories
- **Armor**: Slot 42 - For bulletproof vests and body armor
- **Bag**: Slot 43 - For backpacks and carrying equipment

## Layout Structure
```
[Hotslot] [Left Inventory] [Controls] [Right Inventory] [Clothing Boxes]
                                                        ┌─────────────┐
                                                        │    MASK     │
                                                        │   [slot]    │
                                                        ├─────────────┤
                                                        │   ARMOR     │
                                                        │   [slot]    │
                                                        ├─────────────┤
                                                        │    BAG      │
                                                        │   [slot]    │
                                                        └─────────────┘
```

## Technical Notes
- Uses existing inventory slot system for full compatibility
- Slot numbers 41-43 chosen to avoid conflicts with hotbar (1-5) and standard inventory
- Component follows React best practices with TypeScript
- CSS uses existing SCSS variables for consistency
- No breaking changes to existing functionality

## Usage
The clothing boxes will automatically appear when the inventory is opened. Players can:
- Drag clothing items to the appropriate boxes
- Drag items from boxes back to inventory
- Right-click for context menu options
- View item tooltips by hovering

## Future Enhancements
- Could be extended to support more clothing types
- Slot numbers could be made configurable
- Could add specific item type validation for each box
- Could integrate with clothing/appearance systems
