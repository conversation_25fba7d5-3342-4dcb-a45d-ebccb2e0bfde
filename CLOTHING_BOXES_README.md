# Clothing Boxes Implementation for ox_inventory

## Overview
Completely redesigned the ox_inventory layout with:
1. **3 Clothing Boxes** on the far right that look exactly like inventory grids:
   - **Mask** - Slot 41
   - **Armor** - Slot 42
   - **Bag** - Slot 43
2. **Main inventory changed to 5x3 grid** (was 5x5)
3. **Reorganized layout**: All main inventory components moved to the left, clothing boxes on the far right

## Files Modified

### 1. `web/src/components/inventory/ClothingBoxes.tsx` (NEW)
- New React component that renders 3 clothing boxes that look exactly like inventory grids
- Each box uses the same `inventory-grid-wrapper` and header structure as main inventory
- Uses `InventorySlot` component for full compatibility
- Uses slots 41-43 to avoid conflicts with hotbar slots (1-5)
- Includes proper icons and labels for each clothing type

### 2. `web/src/components/inventory/index.tsx`
- Completely restructured layout with two main sections:
  - `inventory-left-section`: Contains hotslot, main inventory, controls, and right inventory
  - `clothing-boxes-wrapper`: Contains the clothing boxes on the far right
- Moved hotslot container inside the left section

### 3. `web/src/index.scss`
- **Changed main inventory grid**: `$gridRowsL: 3` (was 5) for 5x3 layout
- **Updated layout classes**:
  - `.inventory-wrapper` - Now uses `justify-content: space-between` and padding
  - `.inventory-left-section` - New container for main inventory components
  - `.clothing-boxes-container` - Container for clothing boxes with proper spacing
  - `.clothing-grid-container` - Grid layout for individual clothing slots
- **Removed old clothing box styles** and replaced with inventory-grid-wrapper usage

## Features

### Visual Design
- **Identical to Inventory**: Clothing boxes use the exact same styling as inventory grids
- **Proper Headers**: Each box has an icon and label just like inventory grids
- **Vertical Layout**: 3 boxes stacked vertically on the far right side
- **Consistent Theme**: Perfect match with existing ox_inventory dark theme
- **Responsive**: Uses the same grid system and sizing as main inventory

### Functionality
- **Drag & Drop**: Full integration with existing drag-and-drop system
- **Item Display**: Shows item icons, counts, and weights
- **Context Menu**: Right-click functionality for item actions
- **Tooltips**: Item information tooltips on hover

### Slot Configuration
- **Mask**: Slot 41 - For face masks and head accessories
- **Armor**: Slot 42 - For bulletproof vests and body armor
- **Bag**: Slot 43 - For backpacks and carrying equipment

## Layout Structure
```
LEFT SECTION                                    RIGHT SECTION
┌─────────────────────────────────────────┐    ┌─────────────┐
│  [Hotslot - 5 slots horizontal]        │    │ ┌─────────┐ │
│                                         │    │ │  MASK   │ │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │    │ │ [slot]  │ │
│  │  MAIN   │ │CONTROLS │ │  RIGHT  │   │    │ └─────────┘ │
│  │INVENTORY│ │         │ │INVENTORY│   │    │ ┌─────────┐ │
│  │  5x3    │ │         │ │  5x5    │   │    │ │ ARMOR   │ │
│  │ GRID    │ │         │ │  GRID   │   │    │ │ [slot]  │ │
│  └─────────┘ └─────────┘ └─────────┘   │    │ └─────────┘ │
└─────────────────────────────────────────┘    │ ┌─────────┐ │
                                               │ │   BAG   │ │
                                               │ │ [slot]  │ │
                                               │ └─────────┘ │
                                               └─────────────┘
```

## Technical Notes
- **Perfect Integration**: Uses exact same `inventory-grid-wrapper` and `InventorySlot` components
- **Grid System**: Main inventory changed from 5x5 to 5x3 using `$gridRowsL: 3`
- **Layout Architecture**: Split into left section (main inventory) and right section (clothing)
- **Slot Numbers**: 41-43 chosen to avoid conflicts with hotbar (1-5) and standard inventory
- **TypeScript**: Full type safety with existing inventory interfaces
- **SCSS Variables**: Uses all existing variables for perfect consistency
- **No Breaking Changes**: All existing functionality preserved

## Usage
The clothing boxes will automatically appear when the inventory is opened. Players can:
- Drag clothing items to the appropriate boxes
- Drag items from boxes back to inventory
- Right-click for context menu options
- View item tooltips by hovering

## Future Enhancements
- Could be extended to support more clothing types
- Slot numbers could be made configurable
- Could add specific item type validation for each box
- Could integrate with clothing/appearance systems
