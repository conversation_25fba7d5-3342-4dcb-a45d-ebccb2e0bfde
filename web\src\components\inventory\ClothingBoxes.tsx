import React from 'react';
import InventorySlot from './InventorySlot';
import { useAppSelector } from '../../store';
import { selectLeftInventory } from '../../store/inventory';

const ClothingBoxes: React.FC = () => {
  const leftInventory = useAppSelector(selectLeftInventory);

  // Define the clothing slots - using higher slot numbers to avoid conflict with hotbar (slots 1-5)
  const clothingSlots = {
    mask: { slot: 41, label: 'Mask' },
    armor: { slot: 42, label: 'Armor' },
    bag: { slot: 43, label: 'Bag' }
  };

  // Get the clothing items from the player inventory
  const getClothingItem = (slotNumber: number) => {
    return leftInventory.items.find(item => item.slot === slotNumber) || { slot: slotNumber };
  };

  return (
    <div className="clothing-boxes-wrapper">
      {/* Mask Box */}
      <div className="clothing-box">
        <div className="clothing-box-header">
          <p>{clothingSlots.mask.label}</p>
        </div>
        <InventorySlot
          item={getClothingItem(clothingSlots.mask.slot)}
          inventoryType={leftInventory.type}
          inventoryGroups={leftInventory.groups}
          inventoryId={leftInventory.id}
        />
      </div>

      {/* Armor Box */}
      <div className="clothing-box">
        <div className="clothing-box-header">
          <p>{clothingSlots.armor.label}</p>
        </div>
        <InventorySlot
          item={getClothingItem(clothingSlots.armor.slot)}
          inventoryType={leftInventory.type}
          inventoryGroups={leftInventory.groups}
          inventoryId={leftInventory.id}
        />
      </div>

      {/* Bag Box */}
      <div className="clothing-box">
        <div className="clothing-box-header">
          <p>{clothingSlots.bag.label}</p>
        </div>
        <InventorySlot
          item={getClothingItem(clothingSlots.bag.slot)}
          inventoryType={leftInventory.type}
          inventoryGroups={leftInventory.groups}
          inventoryId={leftInventory.id}
        />
      </div>
    </div>
  );
};

export default ClothingBoxes;
